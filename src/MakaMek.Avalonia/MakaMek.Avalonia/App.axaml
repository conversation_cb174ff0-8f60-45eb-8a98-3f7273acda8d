<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="Sanet.MakaMek.Avalonia.App"
             xmlns:di="using:Microsoft.Extensions.DependencyInjection"
             xmlns:converters="clr-namespace:Sanet.MakaMek.Avalonia.Converters"
             RequestedThemeVariant="Light">

    <Application.Resources>
        <ResourceDictionary>
            <di:ServiceCollection x:Key="ServiceCollection"/>
            <FontFamily x:Key="AwesomeFontSolid">avares://Sanet.MakaMek.Avalonia/Assets/Fonts/#Font Awesome 6 Free Solid</FontFamily>
            <converters:ComponentStatusBackgroundConverter x:Key="ComponentStatusBackgroundConverter"/>
            <converters:HexColorToBrushConverter x:Key="HexColorToBrushConverter"/>
            <converters:ContrastingForegroundConverter x:Key="ContrastingForegroundConverter"/>
            <converters:GreaterThanValueConverter x:Key="GreaterThanValueConverter"/>
            <converters:CollectionToVisibilityConverter x:Key="CollectionToVisibilityConverter"/>
            <converters:BooleanToColorConverter x:Key="BooleanToColorConverter"/>
            <converters:ConsciousnessStatusConverter x:Key="ConsciousnessStatusConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <ResourceInclude Source="avares://Sanet.MakaMek.Avalonia/Styles/Colors.axaml"/>
                <ResourceInclude Source="avares://Sanet.MakaMek.Avalonia/Styles/Animations.axaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
             
    <Application.Styles>
        <FluentTheme />
        <StyleInclude Source="Styles/Theme.axaml"/>
        <StyleInclude Source="Views/TemplatedControls/GamePanel.axaml"/>
        <StyleInclude Source="Views/TemplatedControls/ActionButton.axaml"/>
    </Application.Styles>
</Application>
